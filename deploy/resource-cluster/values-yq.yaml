region: yq
releaseVersion: RELEASE_VERSION # 发布版本号，cce_stack_control.sh 会进行变量替换

enableAlertHook: true
enableCpromControllerV2: false
enableCpromController: true
enableCpromManager: true
enableInstallCRD: true
enableVMOperator: true
enableWriteProxy: true
enableQueryProxy: true
enableAlertAdaptor: true
enableConsoleGrafana: false
enableMigrateController: true
enableCpromBilling: true

cpromBilling:
  schedule: "0 * * * *"
  jobImageID: billing-job.image.repository:billing-job.image.tag


migrateController:
  imageID: cprom-migrate-controller.image.repository:cprom-migrate-controller.image.tag
  replicas: 0
  blackAccount:
    - 0c0b3c9dbb6e41308d3bfd587d908922
    - 1d460bfea2814f1a964dff051212c8d1
    - 2b03a09c1e7f43988d421f4f65eed25f
    - 4e0bf22a82d140258ff287fa62fc41c2
    - f7ac4b5b395846389a889f7b89e9f030
    - 89564256b4ee49e4bb83a8bc5c8b252c
    - 8ba8fe2d049948d88d04918b78ba820c
    - 9d392ebe3ab54e79a5771717cd444eee
    - cd98632863eb4e0c9284b4b17b31bfa5
    - 4f0c8ed2af5440729f9aaf62b75efc09
    - d2904161df1948d58f1d029e2dfa052d
    - dec85e87a5664f7fbcbc885f4b39af78
    - 7d2613e2ec474d18af32d3913b0a3869
    - 7ece49f655ef4b168adfbedb07ddc560
    - b9ca12ddf6064c5eab7961d32496d564
    - 2e1be1eb99e946c3a543ec5a4eaa7d39

consoleGrafana:
  provisioningImage: console-grafana-provisioning.image.repository:console-grafana-provisioning.image.tag
  enableProvisioning: true
  grafanaImageID: registry.baidubce.com/cce-plugin-dev/grafana:v7.5.17-console
  databaseType: postgres
  databaseHost: console-grafana.rdsg2chder7z632.rds.yq.baidubce.com:3306
  databaseUser: grafana
  databasePassword: avbd123456
  databaseName: postgres
  adminPassword: admin@cprom@system
  service: console-grafana.cprom-system.svc.cluster.local:3000/api/cce/service/v2/grafana/proxy/

englishConsoleGrafana:
  provisioningImage: console-grafana-english-provisioning.image.repository:console-grafana-english-provisioning.image.tag
  grafanaImageID: registry.baidubce.com/cce-plugin-dev/grafana:v7.5.17-console
  databaseType: postgres
  databaseHost: console-grafana-english-dev.rdsgqjgqouptqq8.rds.gz.baidubce.com:3306
  databaseUser: grafana
  databasePassword: avbd123456!
  databaseName: postgres
  adminPassword: admin@cprom@system
  service: english-console-grafana.cprom-system.svc.cluster.local:3000/api/cce/service/v2/grafana/proxy/

cpromManager:
  imageID: cprom-manager.image.repository:cprom-manager.image.tag
  redisAddr: redis.fnqvgrrzjwlm.scs.yq.baidubce.com:6379
  redisPassword: "reDis4cProm"
  redisDatabase: 0
  replicas: 3
  mysqlHost: mysql57.rdsmkxepvec24k0.rds.yq.baidubce.com
  mysqlPort: 3306
  mysqlUserName: "cprom"
  mysqlPassword: "mySql4Cprom"
  mysqlDatabase: "cprom"

writeProxy:
  imageID: registry.baidubce.com/cprom-stack-pro/write-proxy:********
  replicas: 5
  lbName: write-proxy-yq
  mysqlHost: mysql57.rdsmkxepvec24k0.rds.yq.baidubce.com
  mysqlPort: 3306
  mysqlUserName: "cprom"
  mysqlPassword: "mySql4Cprom"
  mysqlDatabase: "cprom"
  cpuRequest: "1"
  cpuLimit: "4"
  memRequest: 2Gi
  memLimit: 10Gi


queryProxy:
  imageID: registry.baidubce.com/cprom-stack-pro/query-proxy:********
  replicas: 4
  lbName: query-proxy-yq
  mysqlHost: mysql57.rdsmkxepvec24k0.rds.yq.baidubce.com
  mysqlPort: 3306
  mysqlUserName: "cprom"
  mysqlPassword: "mySql4Cprom"
  mysqlDatabase: "cprom"
  cpuRequest: "1"
  cpuLimit: "4"
  memRequest: 2Gi
  memLimit: 16Gi


cpromController:
  enableConsoleGrafanaTask: true
  appBLBEndpoint: blb.yq.baidubce.com/v1
  openCCEGatewayEndpoint: cce-gateway.yq.baidubce.com
  resourceClusterID: cce-0h3kzh0z
  imageID: cprom-controller.image.repository:cprom-controller.image.tag
  reserveNamespaceHours: 240
  enableLeaderElection: true
  enableCPromIngressLB: true
  cpromIngressLB: lb-c75a0f1b
  useBCECert: true
  replicas: 3
  regionID: "a"
  redisAddr: redis.fnqvgrrzjwlm.scs.yq.baidubce.com:6379
  redisPassword: "reDis4cProm"
  redisDatabase: 0
  stream: bills
  vmSelectEndpoint: http://**************:8481/select/0/prometheus
  grafanaInsideEndpoint: http://query-proxy-service.cprom-system:8818/select/prometheus
  timeSpec: "@daily"
  mysqlHost: mysql57.rdsmkxepvec24k0.rds.yq.baidubce.com
  mysqlPort: 3306
  mysqlUserName: "cprom"
  mysqlPassword: "mySql4Cprom"
  mysqlDatabase: "cprom"

alertHook:
  redisAddr: redis.fnqvgrrzjwlm.scs.yq.baidubce.com:6379
  redisPassword: "reDis4cProm"
  redisDatabase: 0
  stream: alerts
  imageID: alert-hook.image.repository:alert-hook.image.tag
  replicas: 1

VMOperator:
  imageID: registry.baidubce.com/cce-plugin-pro/vm-operator:V0.16.0-betav2-amd64
  replicas: 3
  cpuRequest: "2"
  cpuLimit: "4"
  memRequest: 4Gi
  memLimit: 16Gi

alertAdaptor:
  redisAddr: redis.fnqvgrrzjwlm.scs.yq.baidubce.com:6379
  redisPassword: "reDis4cProm"
  redisDatabase: 0
  mysqlHost: mysql57.rdsmkxepvec24k0.rds.yq.baidubce.com
  mysqlPort: 3306
  mysqlUserName: "cprom"
  mysqlPassword: "mySql4Cprom"
  mysqlDatabase: "cprom"
  image:  # 其他模块，比如alertHook，的镜像版本是在流水线中配置的，每次跑流水线会自动生成镜像版本，因此他们的imageID都是固定写法，没有具体值
    registry: registry.baidubce.com
    image: cce-plugin-dev/alert-adaptor
    tag: v0.0.14  # 目前因为mi中还有alert-adaptor组件，需要手动生成镜像。为了保持两边镜像一致，暂时采用手动生成镜像 + 更新tag的方法。后续可以采用自动生成镜像版本的方案
  replicaCount: 3
