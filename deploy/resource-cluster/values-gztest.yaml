region: gztest
releaseVersion: RELEASE_VERSION # 发布版本号，cce_stack_control.sh 会进行变量替换
env: sandbox

enableAlertHook: true
enableCpromControllerV2: true
enableCpromController: true
enableCpromManager: true
enableInstallCRD: true
enableVMOperator: true
enableWriteProxy: true
enableQueryProxy: true
enableAlertAdaptor: true
enableConsoleGrafana: false
enableMigrateController: true
enableCpromBilling: true

cpromBilling:
  schedule: "0 * * * *"
  jobImageID: billing-job.image.repository:billing-job.image.tag

migrateController:
  imageID: cprom-migrate-controller.image.repository:cprom-migrate-controller.image.tag
  replicas: 0
  blackAccount:
    - 0c0b3c9dbb6e41308d3bfd587d908922
    - 1d460bfea2814f1a964dff051212c8d1
    - 2b03a09c1e7f43988d421f4f65eed25f
    - 4e0bf22a82d140258ff287fa62fc41c2
    - f7ac4b5b395846389a889f7b89e9f030
    - 89564256b4ee49e4bb83a8bc5c8b252c
    - 8ba8fe2d049948d88d04918b78ba820c
    - 9d392ebe3ab54e79a5771717cd444eee
    - cd98632863eb4e0c9284b4b17b31bfa5
    - 4f0c8ed2af5440729f9aaf62b75efc09
    - d2904161df1948d58f1d029e2dfa052d
    - dec85e87a5664f7fbcbc885f4b39af78
    - 7d2613e2ec474d18af32d3913b0a3869
    - 7ece49f655ef4b168adfbedb07ddc560
    - b9ca12ddf6064c5eab7961d32496d564
    - 2e1be1eb99e946c3a543ec5a4eaa7d39

consoleGrafana:
  provisioningImage: console-grafana-provisioning.image.repository:console-grafana-provisioning.image.tag
  enableProvisioning: false
  grafanaImageID: registry.baidubce.com/cce-plugin-dev/grafana:v7.5.17-console
  databaseType: postgres
  databaseHost: console-grafana.rdsgtscfg3aj1te.rds.gz.baidubce.com:3306
  databaseUser: grafana
  databasePassword: avbd123456
  databaseName: postgres
  adminPassword: admin@cprom@system
  service: console-grafana.cprom-system.svc.cluster.local:3000/api/cce/service/v2/grafana/proxy/

englishConsoleGrafana:
  provisioningImage: console-grafana-english-provisioning.image.repository:console-grafana-english-provisioning.image.tag
  grafanaImageID: registry.baidubce.com/cce-plugin-dev/grafana:v7.5.17-console
  databaseType: postgres
  databaseHost: console-grafana-english-dev.rdsgqjgqouptqq8.rds.gz.baidubce.com:3306    # 
  databaseUser: grafana
  databasePassword: avbd123456!
  databaseName: postgres
  adminPassword: admin@cprom@system
  service: english-console-grafana.cprom-system.svc.cluster.local:3000/api/cce/service/v2/grafana/proxy/

cpromManager:
  imageID: cprom-manager.image.repository:cprom-manager.image.tag
  redisAddr: ***********:6379
  redisPassword: "reDis4cProm"
  redisDatabase: 0
  replicas: 1
  mysqlHost: "***********"
  mysqlPort: 3306
  mysqlUserName: "cprom"
  mysqlPassword: "mySql4Cprom"
  mysqlDatabase: "cprom_gztest"



writeProxy:
  imageID: write-proxy.image.repository:write-proxy.image.tag
  replicas: 2
  lbName: write-proxy-gztest
  mysqlHost: "***********"
  mysqlPort: 3306
  mysqlUserName: "cprom"
  mysqlPassword: "mySql4Cprom"
  mysqlDatabase: "cprom_gztest"
  cpuRequest: 1100m
  cpuLimit: 1500m
  memRequest: 1Gi
  memLimit: 6Gi


queryProxy:
  imageID: query-proxy.image.repository:query-proxy.image.tag
  replicas: 1
  lbName: query-proxy-gztest
  mysqlHost: "***********"
  mysqlPort: 3306
  mysqlUserName: "cprom"
  mysqlPassword: "mySql4Cprom"
  mysqlDatabase: "cprom_gztest"
  cpuRequest: 1100m
  cpuLimit: 1500m
  memRequest: 900Mi
  memLimit: 2000Mi


cpromController:
  enableConsoleGrafanaTask: true
  enableEnglishConsoleGrafanaTask: true
  appBLBEndpoint: blb.gz.baidubce.com/v1
  openCCEGatewayEndpoint: cce-gateway.gz.baidubce.com
  resourceClusterID: cce-14xrihgd
  imageID: cprom-controller.image.repository:cprom-controller.image.tag
  reserveNamespaceHours: 1
  enableLeaderElection: false
  enableCPromIngressLB: true
  cpromIngressLB: lb-16575fdb
  replicas: 1
  regionID: "1"
  redisAddr: ***********:6379
  redisPassword: "reDis4cProm"
  redisDatabase: 0
  stream: bills
  vmSelectEndpoint: http://**************:8481/select/0/prometheus
  grafanaInsideEndpoint: http://query-proxy-service.cprom-system:8818/select/prometheus
  timeSpec: "@daily"
  subNetID: ""
  mysqlHost: "***********"
  mysqlPort: 3306
  mysqlUserName: "cprom"
  mysqlPassword: "mySql4Cprom"
  mysqlDatabase: "cprom_gztest"

alertHook:
  redisAddr: ***********:6379
  redisPassword: "reDis4cProm"
  redisDatabase: 0
  stream: alerts
  imageID: alert-hook.image.repository:alert-hook.image.tag
  replicas: 1

VMOperator:
  imageID: registry.baidubce.com/cce-plugin-pro/vm-operator:V0.16.0-betav2-amd64
  replicas: 1
  cpuRequest: 100m
  cpuLimit: 1200m
  memRequest: 200Mi
  memLimit: 3200Mi

alertAdaptor:
  redisAddr: ***********:6379
  redisPassword: "reDis4cProm"
  redisDatabase: 0
  mysqlHost: "***********"
  mysqlPort: 3306
  mysqlUserName: "cprom"
  mysqlPassword: "mySql4Cprom"
  mysqlDatabase: "cprom_gztest"
  image:  # 其他模块，比如alertHook，的镜像版本是在流水线中配置的，每次跑流水线会自动生成镜像版本，因此他们的imageID都是固定写法，没有具体值
    registry: registry.baidubce.com
    image: cce-plugin-dev/alert-adaptor
    tag: v0.0.14  # 目前因为mi中还有alert-adaptor组件，需要手动生成镜像。为了保持两边镜像一致，暂时采用手动生成镜像 + 更新tag的方法。后续可以采用自动生成镜像版本的方案
  replicaCount: 3