{{- if .Values.enableEnglishConsoleGrafana }}
---
apiVersion: v1
data:
  grafana.ini: |
    [server]
    root_url=http://0.0.0.0:3000/api/cce/service/v2/grafana/proxy/
    serve_from_sub_path=true

    [users]
    allow_sign_up = false
    auto_assign_org = true
    auto_assign_org_role = Viewer

    [auth.proxy]
    enabled = true
    header_name = X-WEBAUTH-USER
    header_property = username
    auto_sign_up = true
    enable_login_token = true

    [security]
    allow_embedding=true
    cookie_samesite=lax
    admin_password = {{.Values.englishConsoleGrafana.adminPassword}}
    admin_user = admin
kind: ConfigMap
metadata:
  name: console-grafana-config
  namespace: cprom-system
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    service.beta.kubernetes.io/cce-blb-type: appblb
    service.beta.kubernetes.io/cce-load-balancer-allocate-vip: "true"
    service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "true"
  name: english-console-grafana
  namespace: cprom-system
spec:
  externalTrafficPolicy: Local
  ports:
    - name: http
      port: 3000
      protocol: TCP
      targetPort: 3000
  selector:
    app: english-console-grafana
  sessionAffinity: None
  type: LoadBalancer
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: english-grafana
  name: english-console-grafana
  namespace: cprom-system
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: english-console-grafana
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: english-console-grafana
    spec:
      containers:
        - args:
            - --homepath=/usr/share/grafana
            - --config=/etc/console-grafana/grafana.ini
          env:
            - name: GF_DATABASE_TYPE
              value: {{.Values.englishConsoleGrafana.databaseType}}
            - name: GF_DATABASE_HOST
              value: {{.Values.englishConsoleGrafana.databaseHost}}
            - name: GF_DATABASE_USER
              value: {{.Values.englishConsoleGrafana.databaseUser}}
            - name: GF_DATABASE_PASSWORD
              value: {{.Values.englishConsoleGrafana.databasePassword}}
            - name: GF_DATABASE_NAME
              value: {{.Values.englishConsoleGrafana.databaseName}}
          image: {{.Values.englishConsoleGrafana.grafanaImageID}}
          imagePullPolicy: Always
          {{- if .Values.englishConsoleGrafana.enableProvisioning}}
          lifecycle:
            postStart:
              exec:
                command:
                - /bin/sh
                - -c
                - | 
                  cp -r /console/grafana/provisioning/* /etc/grafana/provisioning/
          {{- end }}
          name: grafana
          ports:
            - containerPort: 3000
              name: http-grafana
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /robots.txt
              port: 3000
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 2
          resources:
            limits:
              cpu: "1"
              memory: 2Gi
            requests:
              cpu: 100m
              memory: 128Mi
          volumeMounts:
          - mountPath: /etc/console-grafana/
            name: console-grafana-config
          {{- if .Values.consoleGrafana.enableProvisioning}}
          - mountPath: /console/grafana/provisioning
            name: console-grafana-provisoning
          {{- end }}    
          securityContext:
            procMount: Default
            runAsUser: 0

      {{- if .Values.englishConsoleGrafana.enableProvisioning}}
      initContainers:
      - command:
        - /bin/sh
        - -c
        - cp -r /grafana/provisioning/* /console/grafana/provisioning/
        image: "{{.Values.englishConsoleGrafana.provisioningImage}}"
        imagePullPolicy: Always
        name: console-grafana-provisioning
        resources: {}
        volumeMounts:
        - mountPath: /console/grafana/provisioning
          name: console-grafana-provisoning
      {{- end }}
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      volumes:
        - name: console-grafana-config
          configMap:
            name: console-grafana-config
        {{- if .Values.englishConsoleGrafana.enableProvisioning}}
        - emptyDir: {}
          name: console-grafana-provisoning
        {{- end }}
{{- end}}            
