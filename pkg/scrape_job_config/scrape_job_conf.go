package scrape_job_config

import cpromv1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/cprom/v1"

// 2022.11.1日更新：默认采集任务仅采集免费指标，免费指标列表见https://cloud.baidu.com/doc/CProm/s/Bl5uu1n14
// 2023.7.27日更新：在正则表达式中加换行会导致换行后的第一个指标不能匹配【好未来case】，整体进行修复
var TelegrafScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "cce-telegraf",
		Content: `job_name: 'cce-telegraf'
scheme: https
kubernetes_sd_configs:
  - role: endpoints
    namespaces:
      names:
        - cce-monitor
tls_config:
  ca_file: /etc/prometheus/cert/root.pem
  cert_file: /etc/prometheus/cert/telegraf-client.pem
  key_file: /etc/prometheus/cert/telegraf-client-key.pem
  insecure_skip_verify: true
relabel_configs:
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_endpoints_name]
  action: keep
  regex: cce-telegraf
- action: labelmap
  regex: __meta_kubernetes_service_label_(.+)
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (container_cpu_millicores|container_cpu_usage_core_nanoseconds|container_cpu_usage_nanocores|
       |container_memory_bytes|container_memory_rss_bytes|container_memory_usage_bytes|container_memory_working_set_bytes)
`,
	},
}

var EtcdScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "etcd",
		Content: `job_name: 'etcd'
scheme: https
kubernetes_sd_configs:
  - role: endpoints
    namespaces:
      names:
        - cce-monitor
tls_config:
  ca_file: /etc/prometheus/etcd/cert/ca.pem
  cert_file: /etc/prometheus/etcd/cert/etcd.pem
  key_file: /etc/prometheus/etcd/cert/etcd-key.pem
  insecure_skip_verify: true
relabel_configs:
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_endpoints_name]
  action: keep
  regex: etcd-targets
- action: labelmap
  regex: __meta_kubernetes_service_label_(.+)
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (etcd_server_has_leader|etcd_server_is_leader|etcd_server_leader_changes_seen_total|
       |etcd_mvcc_db_total_size_in_bytes|etcd_mvcc_db_total_size_in_use_in_bytes|etcd_debugging_mvcc_keys_total|
       |etcd_disk_backend_commit_duration_seconds_bucket|etcd_server_proposals_committed_total|etcd_server_proposals_failed_total|
       |etcd_server_proposals_pending|etcd_server_proposals_applied_total)
`,
	},
}

// controlPlaneScrapeJob - k8s master 节点组件
// Deprecated: 后续统一通过 telegraf 组件获取
var ControlPlaneScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "control-plane-service",
		Content: `job_name: 'control-plane-service'
tls_config:
  ca_file: /etc/prometheus/cert/ca.pem
  cert_file: /etc/prometheus/cert/client-cert.pem
  key_file: /etc/prometheus/cert/client-key.pem
kubernetes_sd_configs:
- role: endpoints
relabel_configs:
- source_labels: [__meta_kubernetes_endpoints_name]
  action: drop
  regex: cce-telegraf
- source_labels: [__meta_kubernetes_service_annotation_control_plane_scrape]
  action: keep
  regex: true
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- source_labels: [__address__, __meta_kubernetes_service_annotation_prometheus_io_port]
  action: replace
  target_label: __address__
  regex: ([^:]+)(?::\d+)?;(\d+)
  replacement: $1:$2
- action: labelmap
  regex: __meta_kubernetes_service_label_(.+)`,
	},
}

// kubernetes-pod默认仅采集node-exporter的免费指标部分
var KubernetesPodsScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "kubernetes-pods",
		Content: `job_name: 'kubernetes-pods'
kubernetes_sd_configs:
  - role: pod
relabel_configs:
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
  action: keep
  regex: true
- source_labels: [__meta_kubernetes_pod_label_app]
  action: keep
  regex: node-exporter
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
  action: replace
  regex: ([^:]+)(?::\d+)?;(\d+)
  replacement: $1:$2
  target_label: __address__
- action: labelmap
  regex: __meta_kubernetes_pod_label_(.+)
- action: labeldrop
  regex: (.+)_revision_hash|(.+)_template_generation
- source_labels: [__meta_kubernetes_pod_node_name]
  target_label: node_name
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (node_infiniband_port_constraint_errors_transmitted_total|node_sockstat_TCP_alloc|node_memory_Buffers_bytes|
       |node_filesystem_files|node_memory_MemTotal_bytes|node_memory_Cached_bytes|
       |node_memory_MemFree_bytes|node_netstat_Tcp_PassiveOpens|node_disk_reads_completed_total|
       |node_cpu_seconds_total|node_disk_written_bytes_total|node_boot_time_seconds|
       |node_memory_MemAvailable_bytes|node_infiniband_port_constraint_errors_received_total|node_infiniband_port_packets_transmitted_total|
       |node_load1|node_filesystem_files_free|node_load15|
       |node_filesystem_size_bytes|node_netstat_Tcp_CurrEstab|node_sockstat_TCP_inuse|
       |node_infiniband_port_data_transmitted_bytes_total|node_infiniband_port_data_received_bytes_total|node_disk_read_bytes_total|
       |node_disk_writes_completed_total|node_infiniband_link_downed_total|node_infiniband_port_packets_received_total|
       |node_load5|node_filesystem_avail_bytes|node_disk_io_time_seconds_total|
       |node_filesystem_free_bytes|node_filefd_allocated|node_sockstat_TCP_tw|
       |node_filefd_maximum|node_netstat_Tcp_ActiveOpens|node_network_transmit_bytes_total|
       |node_network_receive_bytes_total|node_disk_write_time_seconds_total|node_disk_read_time_seconds_total|
       |node_infiniband_info|node_infiniband_legacy_packets_received_total|node_memory_Shmem_bytes)
`,
	},
}

var KubernetesPodsKubeStateScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "kubernetes-pods-kube-state-metrics",
		Content: `job_name: 'kubernetes-pods-kube-state-metrics'
kubernetes_sd_configs:
  - role: pod
relabel_configs:
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
  action: keep
  regex: true
- source_labels: [__meta_kubernetes_pod_label_app]
  action: keep
  regex: kube-state-metrics
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
  action: replace
  regex: ([^:]+)(?::\d+)?;(\d+)
  replacement: $1:$2
  target_label: __address__
- action: labelmap
  regex: __meta_kubernetes_pod_label_(.+)
- action: labeldrop
  regex: (.+)_revision_hash|(.+)_template_generation
metric_relabel_configs:
  # 过滤掉已停止的Pod相关指标，减少无意义的指标采集
  # 针对训练任务等一次性任务，过滤掉Failed和Succeeded状态的Pod指标
  - source_labels: [__name__, phase]
    action: drop
    regex: kube_pod_status_phase;(Failed|Succeeded)
  - source_labels: [__name__, phase]
    action: drop
    regex: kube_pod_info;(Failed|Succeeded)
  - source_labels: [__name__, phase]
    action: drop
    regex: kube_pod_owner;(Failed|Succeeded)
  # 过滤掉已停止Pod的容器相关指标
  - source_labels: [__name__, phase]
    action: drop
    regex: kube_pod_container_status_.*_;(Failed|Succeeded)
  - source_labels: [__name__, phase]
    action: drop
    regex: kube_pod_container_resource_.*_;(Failed|Succeeded)
  # 对于特定的训练任务标签，也进行过滤
  - source_labels: [__name__, job_name, phase]
    action: drop
    regex: kube_pod_.*_;.*training.*;(Failed|Succeeded)
  - source_labels: [__name__, job_name, phase]
    action: drop
    regex: kube_pod_.*_;.*inference.*;(Failed|Succeeded)
  # 对于Job相关的指标，保留Failed和Succeeded状态用于监控
  # 但对于训练任务等一次性任务，可以考虑设置TTL
  - source_labels: [__name__]
    action: keep
    regex: (kube_node_info|kube_node_status_allocatable|kube_deployment_status_replicas_available|
       |kube_statefulset_status_replicas|kube_pod_container_status_terminated|kube_pod_start_time|
       |kube_node_status_condition|kube_job_info|kube_configmap_info|
       |kube_node_spec_unschedulable|kube_daemonset_status_number_unavailable|kube_statefulset_status_replicas_updated|
       |kube_pod_container_resource_limits|kube_deployment_status_replicas_ready|kube_deployment_status_replicas_unavailable|
       |kube_daemonset_status_desired_number_scheduled|kube_persistentvolumeclaim_info|kube_cronjob_status_active|
       |kube_pod_owner|kube_persistentvolumeclaim_status_phase|kube_statefulset_created|
       |kube_pod_container_status_waiting|kube_pod_container_status_restarts_total|kube_secret_info|
       |kube_pod_info|kube_persistentvolumeclaim_resource_requests_storage_bytes|kube_deployment_status_replicas_updated|
       |kube_pod_status_phase|kube_service_info|kube_statefulset_status_replicas_available|
       |kube_cronjob_created|kube_ingress_info|kube_resourcequota|
       |kube_resourcequota_created|kube_statefulset_metadata_generation|kube_node_labels|
       |kube_replicaset_owner|kube_namespace_status_phase|kube_pod_container_resource_requests|
       |kube_namespace_created|kube_pod_created|kube_pod_status_ready|
       |kube_statefulset_replicas|kube_pod_status_unschedulable|kube_job_status_active|
       |kube_daemonset_created|kube_job_status_failed|kube_deployment_created|
       |kube_replicaset_created|kube_service_spec_type|kube_node_status_capacity|
       |kube_deployment_spec_replicas|kube_pod_container_info|kube_statefulset_status_replicas_ready|
       |kube_pod_deletion_timestamp|kube_pod_status_reason|kube_pod_container_status_waiting_reason|
       |kube_deployment_status_replicas)
`,
	},
}

var KubernetesPodsScrapeJobV2 = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeSelf,
		MetricsPath:   "/metrics",
		ScrapeJobName: "kubernetes-pods-user",
		Content: `job_name: 'kubernetes-pods-user'
kubernetes_sd_configs:
  - role: pod
relabel_configs:
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
  action: keep
  regex: true
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- source_labels: [__meta_kubernetes_pod_label_app]
  action: drop
  regex: kube-state-metrics|node-exporter|dcgm-exporter|training-operator
- source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
  action: replace
  regex: ([^:]+)(?::\d+)?;(\d+)
  replacement: $1:$2
  target_label: __address__
- action: labelmap
  regex: __meta_kubernetes_pod_label_(.+)
- action: labeldrop
  regex: (.+)_revision_hash|(.+)_template_generation
`,
	},
}

// gpu-dcgm 采集dcgm-exporter暴露的gpu指标
var GpuScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "gpu-dcgm",
		Content: `job_name: 'gpu-dcgm'
kubernetes_sd_configs:
  - role: pod
relabel_configs:
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
  action: keep
  regex: true
- source_labels: [__meta_kubernetes_pod_label_app]
  action: keep
  regex: dcgm-exporter
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
  action: replace
  regex: ([^:]+)(?::\d+)?;(\d+)
  replacement: $1:$2
  target_label: __address__
- action: labelmap
  regex: __meta_kubernetes_pod_label_(.+)
- action: labeldrop
  regex: (.+)_revision_hash|(.+)_template_generation
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (DCGM_FI_DEV_FB_FREE|DCGM_FI_DEV_FB_USED|DCGM_FI_DEV_GPU_UTIL|
       |DCGM_FI_DEV_GPU_TEMP|DCGM_FI_DEV_POWER_USAGE|DCGM_FI_PROF_NVLINK_RX_BYTES|
       |DCGM_FI_PROF_NVLINK_TX_BYTES|DCGM_FI_PROF_PCIE_RX_BYTES|DCGM_FI_PROF_PCIE_TX_BYTES|
       |DCGM_FI_PROF_PIPE_TENSOR_ACTIVE|DCGM_FI_PROF_SM_ACTIVE|DCGM_FI_PROF_SM_OCCUPANCY)
`,
	},
}

var KubeApiserverScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "kube-apiserver",
		Content: `job_name: 'kube-apiserver'
scheme: https
tls_config:
  ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
  insecure_skip_verify: true
bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
kubernetes_sd_configs:
  - role: endpoints
    namespaces:
      names:
        - cce-monitor
relabel_configs:
- source_labels: [__meta_kubernetes_endpoints_name]
  action: keep
  regex: kube-apiserver-targets
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- action: labelmap
  regex: __meta_kubernetes_service_label_(.+)
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (apiserver_request_total|apiserver_current_inflight_requests|apiserver_dropped_requests_total|
       |apiserver_request_duration_seconds_bucket|etcd_request_duration_seconds_bucket|apiserver_admission_controller_admission_duration_seconds_bucket|
       |apiserver_admission_webhook_admission_duration_seconds_bucket)
`,
	},
}

var KubeControllerManagerScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "kube-controller-manager",
		Content: `job_name: 'kube-controller-manager'
kubernetes_sd_configs:
  - role: endpoints
    namespaces:
      names:
        - cce-monitor
relabel_configs:
- source_labels: [__meta_kubernetes_endpoints_name]
  action: keep
  regex: kube-controller-manager-targets
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- action: labelmap
  regex: __meta_kubernetes_service_label_(.+)
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (workqueue_adds_total|workqueue_depth|workqueue_queue_duration_seconds_bucket|
       |rest_client_requests_total|rest_client_request_duration_seconds_bucket)
`,
	},
}

var KubeSchedulerScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "kube-scheduler",
		Content: `job_name: 'kube-scheduler'
kubernetes_sd_configs:
  - role: endpoints
    namespaces:
      names:
        - cce-monitor
relabel_configs:
- source_labels: [__meta_kubernetes_endpoints_name]
  action: keep
  regex: kube-scheduler-targets
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- action: labelmap
  regex: __meta_kubernetes_service_label_(.+)
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (scheduler_scheduler_cache_size|scheduler_pending_pods|scheduler_pod_scheduling_attempts_bucket|
       |rest_client_requests_total|rest_client_request_duration_seconds_bucket)
`,
	},
}

var KubeletScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		ScrapeJobName: "kubelet",
		MetricsPath:   "/metrics",
		Content: `job_name: 'kubelet'
scheme: https
tls_config:
  ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
  insecure_skip_verify: true
bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
kubernetes_sd_configs:
  - role: node
relabel_configs:
  - action: labelmap
    regex: __meta_kubernetes_node_label_(.+)
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (volume_manager_total_volumes|storage_operation_(duration_seconds_bucket|
       |duration_seconds_count|errors_total)|rest_client_request_(duration_seconds_bucket|total)
       |process_(cpu_seconds_total|resident_memory_bytes)|kubelet_(cgroup_manager_duration_seconds_count|
       |node_config_error|node_name|pleg_relist_duration_seconds_bucket|pleg_relist_duration_seconds_count|
       |pleg_relist_interval_seconds_bucket|pod_start_duration_seconds_count|pod_worker_duration_seconds_count|
       |running_containers|running_pods|runtime_operations_duration_seconds_bucket|runtime_operations_errors_total|
       |runtime_operations_total))
`,
	},
}

var CadvisorScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		ScrapeJobName: "cadvisor",
		MetricsPath:   "/metrics/cadvisor",
		Content: `job_name: 'cadvisor'
scheme: https
tls_config:
  ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
  insecure_skip_verify: true
bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
metrics_path: /metrics/cadvisor
kubernetes_sd_configs:
  - role: node
relabel_configs:
  - action: labelmap
    regex: __meta_kubernetes_node_label_(.+)
  - source_labels: [ instance ]
    action: replace
    target_label: node
metric_relabel_configs:
  - regex: feature_node.*
    action: labeldrop
  - source_labels: [__name__]
    action: keep
    regex: (container_memory_working_set_bytes|container_fs_writes_total|container_memory_rss|
       |container_sockets|container_network_receive_errors_total|container_fs_reads_total|
       |container_cpu_usage_seconds_total|container_fs_reads_bytes_total|container_spec_memory_limit_bytes|
       |container_network_receive_bytes_total|container_network_transmit_bytes_total|container_fs_writes_bytes_total|
       |container_network_transmit_errors_total|container_cpu_system_seconds_total|container_cpu_user_seconds_total)
`,
	},
}
var NpuScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		ScrapeJobName: "npu-exporter",
		MetricsPath:   "/metrics",
		Content: `job_name: 'npu-exporter'
kubernetes_sd_configs:
  - role: pod
relabel_configs:
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
  action: keep
  regex: true
- source_labels: [__meta_kubernetes_pod_label_app]
  action: keep
  regex: npu-exporter
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
  action: replace
  regex: ([^:]+)(?::\d+)?;(\d+)
  replacement: $1:$2
  target_label: __address__
- action: labelmap
  regex: __meta_kubernetes_pod_label_(.+)
- source_labels: [__meta_kubernetes_pod_host_ip]
  target_label: node_ip
- source_labels: [__meta_kubernetes_pod_node_name]
  target_label: node_name
- action: labeldrop
  regex: (.+)_revision_hash|(.+)_template_generation
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (container_npu_utilization|container_npu_used_memory|container_npu_total_memory|
       |npu_chip_info_hbm_total_memory|npu_chip_info_hbm_used_memory|npu_chip_info_utilization|
       |npu_chip_info_bandwidth_rx|npu_chip_info_bandwidth_tx|npu_chip_info_health_status|
       |npu_chip_info_power|npu_chip_info_temperature)
`,
	},
}

var KubeControllerManagerScrapeJobV2 = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "kube-controller-manager",
		Content: `job_name: 'kube-controller-manager'
scheme: https
tls_config:
  ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
  insecure_skip_verify: true
bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
kubernetes_sd_configs:
  - role: endpoints
    namespaces:
      names:
        - cce-monitor
relabel_configs:
- source_labels: [__meta_kubernetes_endpoints_name]
  action: keep
  regex: kube-controller-manager-targets
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- action: labelmap
  regex: __meta_kubernetes_service_label_(.+)
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (workqueue_adds_total|workqueue_depth|workqueue_queue_duration_seconds_bucket|
       |rest_client_requests_total|rest_client_request_duration_seconds_bucket)
`,
	},
}

var KubeSchedulerScrapeJobV2 = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "kube-scheduler",
		Content: `job_name: 'kube-scheduler'
scheme: https
tls_config:
  ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
  insecure_skip_verify: true
bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
kubernetes_sd_configs:
  - role: endpoints
    namespaces:
      names:
        - cce-monitor
relabel_configs:
- source_labels: [__meta_kubernetes_endpoints_name]
  action: keep
  regex: kube-scheduler-targets
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- action: labelmap
  regex: __meta_kubernetes_service_label_(.+)
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (scheduler_scheduler_cache_size|scheduler_pending_pods|scheduler_pod_scheduling_attempts_bucket|
       |rest_client_requests_total|rest_client_request_duration_seconds_bucket)
`,
	},
}

// training_job默认仅采集训练任务容错等免费指标部分
// 2023.07.27 添加训练任务hang住采集指标
var TrainingJobScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "training-job",
		Content: `job_name: 'training-job'
kubernetes_sd_configs:
  - role: pod
relabel_configs:
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
  action: keep
  regex: true
- source_labels: [__meta_kubernetes_pod_label_app]
  action: keep
  regex: training-operator|(.+)-tensorboard|ft-controller
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
  action: replace
  regex: ([^:]+)(?::\d+)?;(\d+)
  replacement: $1:$2
  target_label: __address__
- action: labelmap
  regex: __meta_kubernetes_pod_label_(.+)
- action: labeldrop
  regex: (.+)_revision_hash|(.+)_template_generation
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: training_operator_(.+)|tb_lm_loss_(.+)|fault_tolerant_(.+)
`,
	},
}

// InferringJobScrapeJob 推理任务采集任务，标识为带有label component:predictor
var InferringJobScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "inferring-job",
		Content: `job_name: 'inferring-job'
kubernetes_sd_configs:
  - role: pod
relabel_configs:
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
  action: keep
  regex: true
- source_labels: [__meta_kubernetes_pod_label_component]
  action: keep
  regex: predictor
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
  action: replace
  regex: ([^:]+)(?::\d+)?;(\d+)
  replacement: $1:$2
  target_label: __address__
- action: labelmap
  regex: __meta_kubernetes_pod_label_(.+)
- action: labeldrop
  regex: (.+)_revision_hash|(.+)_template_generation
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (aiak_cache_usage_cpu|aiak_cache_usage_gpu|nv_gpu_memory_total_bytes|
       |nv_gpu_memory_used_bytes|nv_gpu_power_usage|nv_gpu_utilization|
       |nv_inference_compute_infer_duration_us|nv_inference_count|nv_inference_request_duration_us|
       |nv_inference_request_failure|nv_inference_request_success|aiak_lora_request_count)
`,
	},
}

// 推理服务采集任务，标识为带有label component:aihcpom或cce-inference-service，支持vllm和sglang
var InferenceServiceScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "inference-service",
		Content: `job_name: 'inference-service'
kubernetes_sd_configs:
  - role: pod
relabel_configs:
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
  action: keep
  regex: true
- source_labels: [__meta_kubernetes_pod_label_component]
  action: keep
  regex: aihcpom|cce-inference-service
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
  action: replace
  regex: ([^:]+)(?::\d+)?;(\d+)
  replacement: $1:$2
  target_label: __address__
- source_labels: [__meta_kubernetes_namespace]
  target_label: kubernetes_namespace
- source_labels: [__meta_kubernetes_pod_name]
  target_label: kubernetes_pod_name
- action: labelmap
  regex: __meta_kubernetes_pod_label_(.+)
- action: labeldrop
  regex: (.+)_revision_hash|(.+)_template_generation
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (vllm:time_to_first_token_seconds_sum|vllm:time_to_first_token_seconds_count|sglang:time_to_first_token_seconds_sum|
       |sglang:time_to_first_token_seconds_count|vllm:time_per_output_token_seconds_sum|vllm:time_per_output_token_seconds_count|
       |sglang:time_per_output_token_seconds_sum|sglang:time_per_output_token_seconds_count|vllm:e2e_request_latency_seconds_sum|
       |vllm:e2e_request_latency_seconds_count|sglang:e2e_request_latency_seconds_sum|sglang:e2e_request_latency_seconds_count|
       |vllm:gpu_prefix_cache_hits_total|vllm:gpu_prefix_cache_queries_total|vllm:gpu_prefix_cache_hit_rate|
       |sglang:cache_hit_rate|vllm:request_prompt_tokens_sum|vllm:request_prompt_tokens_count|sglang:prompt_tokens_total|
       |sglang:num_requests_total|vllm:request_generation_tokens_sum|vllm:request_generation_tokens_count|
       |sglang:generation_tokens_total|vllm:num_requests_running|sglang:num_running_reqs|
       |vllm:num_requests_waiting|sglang:num_queue_reqs|sglang:gen_throughput)
`,
	},
}

// VolcanoJobScrapeJob volcano 调度卡信息采集任务
var VolcanoJobScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "volcano",
		Content: `job_name: 'volcano'
kubernetes_sd_configs:
  - role: pod
relabel_configs:
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
  action: keep
  regex: true
- source_labels: [__meta_kubernetes_pod_label_app]
  action: keep
  regex: volcano-scheduler
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
  action: replace
  regex: ([^:]+)(?::\d+)?;(\d+)
  replacement: $1:$2
  target_label: __address__
- action: labelmap
  regex: __meta_kubernetes_pod_label_(.+)
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (volcano_job_creation_timestamp|volcano_node_capacity|volcano_node_idle|
       |volcano_node_status|volcano_node_used|volcano_queue_allocated|
       |volcano_queue_capacity_spec|volcano_queue_deserved|volcano_queue_deserved_spec|
       |volcano_queue_guarantee_spec|volcano_queue_state|volcano_task_resource_detail|
       |volcano_job_info|volcano_job_retry_counts|volcano_job_task_resource|
       |volcano_task_scheduling_latency_milliseconds_bucket|volcano_task_scheduling_latency_milliseconds_count|volcano_task_scheduling_latency_milliseconds_sum|
       |volcano_task_status|volcano_unschedule_task_count|volcano_action_scheduling_latency_microseconds_bucket|
       |volcano_action_scheduling_latency_microseconds_count|volcano_action_scheduling_latency_microseconds_sum|
       |volcano_e2e_job_scheduling_latency_milliseconds_bucket|
       |volcano_e2e_job_scheduling_latency_milliseconds_count|volcano_e2e_job_scheduling_latency_milliseconds_sum|
       |volcano_e2e_scheduling_latency_milliseconds_bucket|
       |volcano_e2e_scheduling_latency_milliseconds_count|volcano_e2e_scheduling_latency_milliseconds_sum)
`,
	},
}

// KubernetesClusterPodJob 集群内所有pod默认采集任务
var KubernetesClusterPodJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "kubernetes-cluster-pods",
		Content: `job_name: 'kubernetes-cluster-pods'
kubernetes_sd_configs:
  - role: pod
relabel_configs:
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape_pods]
  action: keep
  regex: true
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- action: labelmap
  regex: __meta_kubernetes_pod_label_(.+)
- action: labeldrop
  regex: (.+)_revision_hash|(.+)_template_generation
- source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
  action: replace
  regex: ([^:]+)(?::\d+)?;(\d+)
  replacement: $1:$2
  target_label: __address__
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (subnet_available_ip_count|multi_eni_multi_ip_eni_count|multi_eni_multi_ip_eniip_count|
       |primary_eni_multi_ip_eniip_allocated_count|primary_eni_multi_ip_eniip_total_count|primary_eni_multi_ip_eniip_available_count|
       |cni_rpc_latency_bucket|cni_rpc_concurrency|bce_openapi_latency_bucket|
       |memory_usage_percent|cni_rpc_rejected_count|cni_rpc_error_count)
`,
	},
}

// InferenceControllerScrapeJob cce推理队列采集任务，标识为带有label app:cce-inference-controller
var InferenceControllerScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "inference-controller",
		Content: `job_name: 'inference-controller'
kubernetes_sd_configs:
  - role: pod
relabel_configs:
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
  action: keep
  regex: true
- source_labels: [__meta_kubernetes_pod_label_app]
  action: keep
  regex: cce-inference-controller
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
  action: replace
  regex: ([^:]+)(?::\d+)?;(\d+)
  replacement: $1:$2
  target_label: __address__
- action: labelmap
  regex: __meta_kubernetes_pod_label_(.+)
- action: labeldrop
  regex: (.+)_revision_hash|(.+)_template_generation
`,
	},
}

// corednsScrapeJob coredns采集任务
var CorednsScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "coredns",
		Content: `job_name: 'coredns'
kubernetes_sd_configs:
  - role: endpoints
    namespaces:
      names:
      - kube-system
relabel_configs:
- source_labels: [__config_type]
  regex: service
  target_label: __config_type
- source_labels: [__meta_kubernetes_endpoint_port_name]
  regex: metrics
  action: keep
- source_labels: [__meta_kubernetes_service_name]
  regex: kube-dns
  action: keep
- source_labels: [__meta_kubernetes_pod_node_name]
  target_label: node
- source_labels: [__meta_kubernetes_namespace]
  target_label: namespace
- source_labels: [__meta_kubernetes_service_name]
  target_label: service
- source_labels: [__meta_kubernetes_pod_name]
  target_label: pod
- source_labels: [__meta_kubernetes_endpoint_port_name]
  target_label: endpoint
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (coredns_build_info|coredns_dns_request_count_total|coredns_dns_requests_total|
       |coredns_dns_response_rcode_count_total|coredns_dns_request_type_count_total|coredns_dns_request_do_count_total|
       |coredns_dns_do_requests_total|coredns_dns_request_size_bytes_bucket|coredns_dns_responses_total|
       |coredns_dns_request_duration_seconds_bucket|coredns_dns_response_size_bytes_bucket|coredns_cache_size|
       |coredns_cache_entries|coredns_cache_hits_total|coredns_cache_misses_total)
`,
	},
}

// IngressNginxController ingress nginx controller 采集任务
var IngressNginxController = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "ingress-nginx-endpoints",
		Content: `job_name: 'ingress-nginx-endpoints'
kubernetes_sd_configs:
- role: pod
  namespaces:
    names:
    - kube-system
relabel_configs:
- source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_name]
  separator: ;
  regex: cce-ingress-nginx-controller
  action: keep
- source_labels: [__address__]
  separator: ;
  regex: (.*?)(:\d+)?$
  replacement: ${1}:10254
  target_label: __address__
  action: replace
- source_labels: [__meta_kubernetes_namespace]
  separator: ;
  regex: (.*)
  target_label: kubernetes_namespace
  replacement: $1
  action: replace
- source_labels: [__meta_kubernetes_pod_name]
  separator: ;
  regex: (.*)
  target_label: kubernetes_pod_name
  replacement: $1
  action: replace
metric_relabel_configs:
  - source_labels: [__name__]
    action: keep
    regex: (nginx_ingress_controller_nginx_process_connections|
      |nginx_ingress_controller_requests|nginx_ingress_controller_request_duration_seconds_bucket|
      |nginx_ingress_controller_success|nginx_ingress_controller_config_last_reload_successful|
      |nginx_ingress_controller_config_hash)
`,
	},
}

// xpu-exporter 采集任务
var XpuExporterScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "xpu-exporter",
		Content: `job_name: 'xpu-exporter'
scrape_interval: 15s
scrape_timeout: 15s
kubernetes_sd_configs:
  - role: pod
relabel_configs:
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
  action: keep
  regex: true
- source_labels: [__meta_kubernetes_pod_label_k8s_app]
  action: keep
  regex: .*xpu-exporter.*
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
  action: replace
  regex: ([^:]+)(?::\d+)?;(\d+)
  replacement: $1:$2
  target_label: __address__
- action: labelmap
  regex: __meta_kubernetes_pod_label_(.+)
- source_labels: [__meta_kubernetes_pod_host_ip]
  target_label: node_ip
- source_labels: [__meta_kubernetes_pod_node_name]
  target_label: node_name
- source_labels: [__meta_kubernetes_pod_node_name]
  target_label: node_name
- source_labels: [__meta_kubernetes_pod_node_name]
  target_label: node
- action: labeldrop
  regex: (.+)_revision_hash|(.+)_template_generation
metric_relabel_configs:
  - source_labels: [pod_namespace]
    target_label: namespace
    replacement: '$1'
    regex: (.*)
  - source_labels: [__name__]
    action: keep
    regex: (node_container_xpu_memtotal|node_container_xpu_memused|node_container_xpu_utilization|
       |node_container_xpu_memutil|node_xpu_utilization|node_xpu_powerUsage|
       |node_xpu_temp|node_xpu_memfree|node_xpu_memtotal|node_xpu_memused|node_xpu_memutil|node_xpu_state)
`,
	},
}

// FtagentScrapeJob  ftagent 采集任务
var FtagentScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "ftagent",
		Content: `job_name: "ftagent"
scheme: http
scrape_interval: 15s
honor_timestamps: true
metrics_path: /metrics
kubernetes_sd_configs:
  - role: pod
relabel_configs:
  - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
    action: keep
    regex: true
  - source_labels: [__meta_kubernetes_pod_label_training_kubeflow_org_operator_name]
    action: keep
    regex: "pytorchjob-controller"
  - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
    action: replace
    target_label: metrics_path
    regex: (.+)
  - source_labels: [__meta_kubernetes_pod_host_ip]
    action: replace
    target_label: host_ip
    regex: (.+)
  - source_labels: [__meta_kubernetes_pod_node_name]
    action: replace
    target_label: node_name
    regex: (.+)
  - source_labels: [__meta_kubernetes_namespace]
    action: replace
    target_label: namespace
  - source_labels:
      [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
    action: replace
    regex: ([^:]+)(?::\d+)?;(\d+)
    replacement: $1:$2
    target_label: __address__
  - action: labelmap
    regex: __meta_kubernetes_pod_label_(.+)
`,
	},
}

// node-remedier 采集任务
var NodeRemedierScrapeJob = cpromv1.ScrapeJob{
	Spec: cpromv1.ScrapeJobSpec{
		Type:          cpromv1.ScrapeJobTypeBasic,
		MetricsPath:   "/metrics",
		ScrapeJobName: "node-remedier",
		Content: `job_name: "node-remedier"
scrape_interval: 15s
scrape_timeout: 15s
kubernetes_sd_configs:
  - role: pod
relabel_configs:
- source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
  action: replace
  target_label: __scheme__
  regex: (https?)
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
  action: keep
  regex: true
- source_labels: [__meta_kubernetes_pod_label_app]
  action: keep
  regex: node-remedier-controller
- source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
  action: replace
  target_label: __metrics_path__
  regex: (.+)
- source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
  action: replace
  regex: ([^:]+)(?::\d+)?;(\d+)
  replacement: $1:$2
  target_label: __address__
- action: labelmap
  regex: __meta_kubernetes_pod_label_(.+)
- source_labels: [__meta_kubernetes_pod_node_name]
  target_label: node
- action: labeldrop
  regex: (.+)_revision_hash|(.+)_template_generation
metric_relabel_configs:
  - source_labels: [pod_namespace]
    target_label: namespace
    replacement: '$1'
    regex: (.*)
  - source_labels: [__name__]
    action: keep
    regex: node_remedier_(.+)
`,
	},
}
