# KSM (kube-state-metrics) 采集优化指南

## 问题背景

在 Kubernetes 集群中，kube-state-metrics (KSM) 会采集大量的集群状态指标，包括已经停止的 Pod（Failed、Succeeded 状态）的指标。对于训练任务等一次性任务，这些已停止的 Pod 会越来越多，导致：

1. **成本增加**：采集大量无用的指标数据，增加存储和计算成本
2. **性能影响**：Prometheus 存储和查询性能下降
3. **存储浪费**：Failed/Succeeded 状态的 Pod 指标占用大量存储空间

## 解决方案

### 1. 指标过滤策略

通过 `metric_relabel_configs` 配置，过滤掉已停止 Pod 的相关指标：

```yaml
metric_relabel_configs:
  # 过滤掉已停止的Pod相关指标
  - source_labels: [__name__, phase]
    action: drop
    regex: kube_pod_status_phase;(Failed|Succeeded)
  - source_labels: [__name__, phase]
    action: drop
    regex: kube_pod_info;(Failed|Succeeded)
  - source_labels: [__name__, phase]
    action: drop
    regex: kube_pod_owner;(Failed|Succeeded)
  # 过滤掉已停止Pod的容器相关指标
  - source_labels: [__name__, phase]
    action: drop
    regex: kube_pod_container_status_.*_;(Failed|Succeeded)
  - source_labels: [__name__, phase]
    action: drop
    regex: kube_pod_container_resource_.*_;(Failed|Succeeded)
```

### 2. 训练任务特殊处理

针对训练任务等特定工作负载，添加额外的过滤规则：

```yaml
  # 对于特定的训练任务标签，也进行过滤
  - source_labels: [__name__, job_name, phase]
    action: drop
    regex: kube_pod_.*_;.*training.*;(Failed|Succeeded)
  - source_labels: [__name__, job_name, phase]
    action: drop
    regex: kube_pod_.*_;.*inference.*;(Failed|Succeeded)
```

### 3. 保留关键指标

保留一些关键的 Job 状态指标用于监控：

```yaml
  # 保留的关键指标（即使 Pod 已停止）
  - source_labels: [__name__]
    action: keep
    regex: (kube_job_status_succeeded|kube_job_status_failed|kube_job_status_active|kube_job_info)
```

## 实施步骤

### 步骤 1: 检查当前状态

```bash
# 检查当前指标数量
./scripts/optimize-ksm-collection.sh check

# 查看已停止的 Pod 数量
kubectl get pods --all-namespaces --field-selector=status.phase=Failed
kubectl get pods --all-namespaces --field-selector=status.phase=Succeeded
```

### 步骤 2: 备份当前配置

```bash
# 创建备份
./scripts/optimize-ksm-collection.sh backup
```

### 步骤 3: 应用优化配置

```bash
# 干运行模式（推荐先执行）
./scripts/optimize-ksm-collection.sh --dry-run apply

# 实际应用配置
./scripts/optimize-ksm-collection.sh apply
```

### 步骤 4: 验证优化效果

```bash
# 验证优化效果
./scripts/optimize-ksm-collection.sh verify
```

### 步骤 5: 监控和调整

监控以下指标来评估优化效果：

1. **指标数量减少**：KSM 相关指标总数应该显著减少
2. **存储使用量**：Prometheus 存储使用量应该下降
3. **查询性能**：Prometheus 查询响应时间应该改善
4. **成本节约**：监控相关的计费指标

## 配置文件说明

### 主要修改的文件

1. **pkg/scrape_job_config/scrape_job_conf.go**
   - 修改 `KubernetesPodsKubeStateScrapeJob` 的 `metric_relabel_configs`
   - 添加 Pod 状态过滤规则

2. **deploy/resource-cluster/values-gztest-ksm-optimization.yaml**
   - 新的优化配置文件
   - 包含 KSM 优化相关的配置参数

3. **scripts/optimize-ksm-collection.sh**
   - 自动化优化脚本
   - 支持备份、应用、验证、回滚等操作

### 配置参数说明

```yaml
ksmOptimization:
  # 启用 Pod 状态过滤
  enablePodStatusFilter: true
  
  # 过滤掉的 Pod 状态
  filteredPodPhases:
    - "Failed"
    - "Succeeded"
  
  # 训练任务相关的过滤规则
  trainingJobFilter:
    enabled: true
    jobNamePatterns:
      - ".*training.*"
      - ".*inference.*"
      - ".*pytorch.*"
      - ".*tensorflow.*"
      - ".*job-.*"
    # 已完成训练任务的指标保留时间（小时）
    completedJobRetentionHours: 24
```

## 风险和注意事项

### 1. 监控盲区

过滤掉已停止 Pod 的指标可能会导致：
- 无法查看历史失败任务的详细信息
- 故障排查时缺少相关指标数据

**缓解措施**：
- 保留关键的 Job 状态指标
- 设置合理的指标保留时间
- 在应用过滤前确保有其他日志收集机制

### 2. 配置复杂性

新的过滤规则增加了配置的复杂性：
- 需要定期审查过滤规则的有效性
- 新的工作负载类型可能需要调整过滤规则

**缓解措施**：
- 使用自动化脚本管理配置
- 建立配置变更的审查流程
- 定期监控过滤效果

### 3. 回滚计划

如果优化配置导致问题，需要能够快速回滚：

```bash
# 回滚到优化前的配置
./scripts/optimize-ksm-collection.sh rollback
```

## 预期效果

根据典型的 Kubernetes 集群，预期可以实现：

1. **指标数量减少**：30-50% 的 KSM 指标数量减少
2. **存储节约**：20-40% 的 Prometheus 存储空间节约
3. **性能提升**：查询响应时间提升 10-30%
4. **成本节约**：监控相关成本降低 20-40%

## 监控和告警

建议设置以下监控和告警：

1. **指标数量监控**：
   ```promql
   count(count by (__name__)({__name__=~"kube_.*"}))
   ```

2. **已停止 Pod 数量告警**：
   ```promql
   count(kube_pod_status_phase{phase=~"Failed|Succeeded"}) > 100
   ```

3. **存储使用量监控**：
   ```promql
   prometheus_tsdb_symbol_table_size_bytes
   ```

## 故障排查

### 常见问题

1. **过滤规则不生效**
   - 检查正则表达式语法
   - 验证标签名称是否正确
   - 确认配置已正确应用

2. **重要指标被误过滤**
   - 检查过滤规则的匹配范围
   - 添加例外规则保留重要指标
   - 调整正则表达式的精确度

3. **性能改善不明显**
   - 检查已停止 Pod 的实际数量
   - 验证过滤规则的覆盖范围
   - 考虑添加更多的过滤规则

### 调试命令

```bash
# 查看当前 KSM 指标
curl -s http://prometheus:9090/api/v1/label/__name__/values | jq '.data[] | select(test("kube_"))'

# 查看特定指标的标签
curl -s "http://prometheus:9090/api/v1/query?query=kube_pod_status_phase" | jq '.data.result[].metric'

# 检查过滤效果
curl -s "http://prometheus:9090/api/v1/query?query=count(count%20by%20(__name__)({__name__=~%22kube_pod.*%22,phase=%22Failed%22}))"
```

## 总结

通过实施 KSM 采集优化，可以显著减少无意义的指标采集，降低 Prometheus 负载和成本。关键是要：

1. 仔细设计过滤规则，避免过度过滤
2. 保留关键的监控指标
3. 建立完善的监控和回滚机制
4. 定期评估和调整优化效果

这个优化方案特别适合有大量训练任务或批处理作业的 Kubernetes 集群。
