#!/bin/bash

# KSM (kube-state-metrics) 采集优化脚本
# 用于过滤已停止服务的指标，减少 Prometheus 负载和成本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 配置变量
NAMESPACE="cprom-system"
BACKUP_DIR="./backup/$(date +%Y%m%d_%H%M%S)"
DRY_RUN=${DRY_RUN:-false}

# 创建备份目录
create_backup() {
    log_info "创建备份目录: $BACKUP_DIR"
    mkdir -p "$BACKUP_DIR"
}

# 备份当前配置
backup_current_config() {
    log_info "备份当前 KSM 相关配置..."
    
    # 备份 scrape job 配置
    if kubectl get configmap -n $NAMESPACE | grep -q "scrape-job"; then
        kubectl get configmap -n $NAMESPACE -o yaml > "$BACKUP_DIR/scrape-job-configmap.yaml"
        log_info "已备份 scrape job configmap"
    fi
    
    # 备份 monitor-agent 配置
    if kubectl get configmap -n $NAMESPACE | grep -q "monitor-agent"; then
        kubectl get configmap -n $NAMESPACE -o yaml > "$BACKUP_DIR/monitor-agent-configmap.yaml"
        log_info "已备份 monitor-agent configmap"
    fi
    
    # 备份当前的 values 文件
    if [ -f "deploy/resource-cluster/values-gztest.yaml" ]; then
        cp "deploy/resource-cluster/values-gztest.yaml" "$BACKUP_DIR/values-gztest-original.yaml"
        log_info "已备份原始 values 文件"
    fi
}

# 检查当前指标数量
check_current_metrics() {
    log_info "检查当前 KSM 指标数量..."
    
    # 获取 Prometheus 查询端点
    PROMETHEUS_ENDPOINT=$(kubectl get svc -n $NAMESPACE -l app=prometheus -o jsonpath='{.items[0].spec.clusterIP}:{.items[0].spec.ports[0].port}' 2>/dev/null || echo "")
    
    if [ -n "$PROMETHEUS_ENDPOINT" ]; then
        log_info "Prometheus 端点: $PROMETHEUS_ENDPOINT"
        
        # 查询 KSM 相关指标数量
        TOTAL_KSM_METRICS=$(curl -s "http://$PROMETHEUS_ENDPOINT/api/v1/query?query=count(count%20by%20(__name__)({__name__=~%22kube_.*%22}))" | jq -r '.data.result[0].value[1]' 2>/dev/null || echo "0")
        FAILED_POD_METRICS=$(curl -s "http://$PROMETHEUS_ENDPOINT/api/v1/query?query=count(count%20by%20(__name__)({__name__=~%22kube_pod.*%22,phase=%22Failed%22}))" | jq -r '.data.result[0].value[1]' 2>/dev/null || echo "0")
        SUCCEEDED_POD_METRICS=$(curl -s "http://$PROMETHEUS_ENDPOINT/api/v1/query?query=count(count%20by%20(__name__)({__name__=~%22kube_pod.*%22,phase=%22Succeeded%22}))" | jq -r '.data.result[0].value[1]' 2>/dev/null || echo "0")
        
        log_info "当前 KSM 指标总数: $TOTAL_KSM_METRICS"
        log_warn "Failed 状态 Pod 指标数: $FAILED_POD_METRICS"
        log_warn "Succeeded 状态 Pod 指标数: $SUCCEEDED_POD_METRICS"
        
        STOPPED_POD_METRICS=$((FAILED_POD_METRICS + SUCCEEDED_POD_METRICS))
        if [ $STOPPED_POD_METRICS -gt 0 ]; then
            PERCENTAGE=$(echo "scale=2; $STOPPED_POD_METRICS * 100 / $TOTAL_KSM_METRICS" | bc -l 2>/dev/null || echo "0")
            log_warn "已停止 Pod 指标占比: ${PERCENTAGE}%"
        fi
    else
        log_warn "无法连接到 Prometheus，跳过指标统计"
    fi
}

# 应用优化配置
apply_optimization() {
    log_info "应用 KSM 采集优化配置..."
    
    if [ "$DRY_RUN" = "true" ]; then
        log_info "DRY RUN 模式，仅显示将要执行的操作"
        log_info "将会更新以下配置:"
        log_info "  - pkg/scrape_job_config/scrape_job_conf.go (已更新)"
        log_info "  - deploy/resource-cluster/values-gztest-ksm-optimization.yaml (已创建)"
        return
    fi
    
    # 重新构建和部署
    log_info "重新构建 cprom-controller..."
    if command -v make >/dev/null 2>&1; then
        make build-cprom-controller || {
            log_error "构建 cprom-controller 失败"
            return 1
        }
    else
        log_warn "未找到 make 命令，请手动构建 cprom-controller"
    fi
    
    # 重启相关服务
    log_info "重启 cprom-controller 以应用新配置..."
    kubectl rollout restart deployment/cprom-controller -n $NAMESPACE || {
        log_error "重启 cprom-controller 失败"
        return 1
    }
    
    # 等待部署完成
    kubectl rollout status deployment/cprom-controller -n $NAMESPACE --timeout=300s || {
        log_error "等待 cprom-controller 重启超时"
        return 1
    }
    
    log_info "KSM 优化配置应用完成"
}

# 验证优化效果
verify_optimization() {
    log_info "验证优化效果..."
    
    # 等待一段时间让新配置生效
    log_info "等待 60 秒让新配置生效..."
    sleep 60
    
    # 再次检查指标数量
    check_current_metrics
    
    # 检查 Pod 状态
    log_info "检查集群中的 Pod 状态分布..."
    kubectl get pods --all-namespaces --field-selector=status.phase=Failed --no-headers 2>/dev/null | wc -l | xargs -I {} log_info "Failed 状态 Pod 数量: {}"
    kubectl get pods --all-namespaces --field-selector=status.phase=Succeeded --no-headers 2>/dev/null | wc -l | xargs -I {} log_info "Succeeded 状态 Pod 数量: {}"
    kubectl get pods --all-namespaces --field-selector=status.phase=Running --no-headers 2>/dev/null | wc -l | xargs -I {} log_info "Running 状态 Pod 数量: {}"
}

# 回滚配置
rollback_config() {
    log_warn "回滚到优化前的配置..."
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_error "备份目录不存在，无法回滚"
        return 1
    fi
    
    # 恢复原始配置文件
    if [ -f "$BACKUP_DIR/values-gztest-original.yaml" ]; then
        cp "$BACKUP_DIR/values-gztest-original.yaml" "deploy/resource-cluster/values-gztest.yaml"
        log_info "已恢复原始 values 文件"
    fi
    
    # 重启服务
    kubectl rollout restart deployment/cprom-controller -n $NAMESPACE
    kubectl rollout status deployment/cprom-controller -n $NAMESPACE --timeout=300s
    
    log_info "配置回滚完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
KSM 采集优化脚本

用法: $0 [选项] [操作]

选项:
  -n, --namespace NAMESPACE    指定命名空间 (默认: cprom-system)
  -d, --dry-run               干运行模式，不实际执行操作
  -h, --help                  显示此帮助信息

操作:
  check                       检查当前指标数量
  backup                      备份当前配置
  apply                       应用优化配置
  verify                      验证优化效果
  rollback                    回滚配置
  all                         执行完整的优化流程 (默认)

示例:
  $0 check                    # 仅检查当前指标数量
  $0 --dry-run apply          # 干运行模式应用配置
  $0 --namespace my-ns all    # 在指定命名空间执行完整流程

EOF
}

# 主函数
main() {
    local action="all"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            check|backup|apply|verify|rollback|all)
                action="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "开始 KSM 采集优化 - 操作: $action, 命名空间: $NAMESPACE"
    
    case $action in
        check)
            check_current_metrics
            ;;
        backup)
            create_backup
            backup_current_config
            ;;
        apply)
            apply_optimization
            ;;
        verify)
            verify_optimization
            ;;
        rollback)
            rollback_config
            ;;
        all)
            create_backup
            backup_current_config
            check_current_metrics
            apply_optimization
            verify_optimization
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
    
    log_info "KSM 采集优化完成"
}

# 执行主函数
main "$@"
