# Pod 状态过滤策略示例
# 展示如何在不同层面过滤已停止的 Pod 指标

# ==========================================
# 方法 1: 在 kube-state-metrics 部署中配置资源过滤
# ==========================================
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kube-state-metrics
  namespace: kube-system
spec:
  template:
    spec:
      containers:
      - name: kube-state-metrics
        image: k8s.gcr.io/kube-state-metrics/kube-state-metrics:v2.6.0
        args:
        # 只监控特定的资源类型
        - --resources=pods,deployments,services,nodes
        # 使用标签选择器过滤 Pod
        - --pod-labels-metric-labelsallowlist=app,version,environment
        # 过滤掉特定命名空间的 Pod
        - --namespace-denylist=kube-system,kube-public
        # 或者只监控特定命名空间
        - --namespaces=default,production,staging
        ports:
        - containerPort: 8080
          name: http-metrics
        - containerPort: 8081
          name: telemetry

---
# ==========================================
# 方法 2: 在 Prometheus scrape 配置中使用 relabel_configs
# ==========================================
# 这个配置用于采集 kube-state-metrics 的指标
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
data:
  prometheus.yml: |
    scrape_configs:
    - job_name: 'kube-state-metrics'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
          - kube-system
      relabel_configs:
      # 只采集 kube-state-metrics Pod
      - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_name]
        action: keep
        regex: kube-state-metrics
      # 设置指标路径
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      # 设置采集端口
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
      
      # ==========================================
      # 方法 3: 使用 metric_relabel_configs 过滤指标
      # ==========================================
      metric_relabel_configs:
      # 过滤掉 Failed 状态的 Pod 指标
      - source_labels: [__name__, phase]
        action: drop
        regex: kube_pod_.*_;Failed
      
      # 过滤掉 Succeeded 状态的大部分 Pod 指标，但保留 Job 相关的
      - source_labels: [__name__, phase]
        action: drop
        regex: kube_pod_(info|owner|start_time|created|status_ready|status_unschedulable|deletion_timestamp|status_reason);Succeeded
      
      # 过滤掉 Succeeded 状态 Pod 的容器相关指标
      - source_labels: [__name__, phase]
        action: drop
        regex: kube_pod_container_(status_.*|resource_.*|info);Succeeded
      
      # 针对训练任务的特殊过滤
      # 通过 job 标签识别训练任务
      - source_labels: [__name__, job, phase]
        action: drop
        regex: kube_pod_.*_;.*-(training|train|inference|infer|pytorch|tensorflow|job)-.*;(Failed|Succeeded)
      
      # 通过 namespace 识别训练相关的命名空间
      - source_labels: [__name__, namespace, phase]
        action: drop
        regex: kube_pod_.*_;(training|ml|ai|batch|jobs?)-.*;(Failed|Succeeded)
      
      # 通过 Pod 名称模式过滤
      - source_labels: [__name__, pod, phase]
        action: drop
        regex: kube_pod_.*_;.*-(job|training|inference)-[a-z0-9]+;(Failed|Succeeded)
      
      # 保留重要的 Job 状态指标
      - source_labels: [__name__]
        action: keep
        regex: kube_job_(status_succeeded|status_failed|status_active|info|spec_completions|spec_parallelism)
      
      # 基于时间的过滤：过滤掉创建时间超过24小时的已完成Pod指标
      # 注意：这需要额外的处理逻辑，Prometheus 本身不支持基于时间的过滤
      
      # 最终的指标白名单
      - source_labels: [__name__]
        action: keep
        regex: (kube_node_.*|kube_deployment_.*|kube_service_.*|kube_job_.*|kube_pod_status_phase|kube_pod_info|kube_pod_owner|kube_pod_container_status_running|kube_pod_container_resource_requests|kube_pod_container_resource_limits)

---
# ==========================================
# 方法 4: 使用 VMPodScrape (VictoriaMetrics) 的高级过滤
# ==========================================
apiVersion: operator.victoriametrics.com/v1beta1
kind: VMPodScrape
metadata:
  name: kube-state-metrics-filtered
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: kube-state-metrics
  podMetricsEndpoints:
  - port: http-metrics
    path: /metrics
    # 使用 filterRunning 参数过滤非运行状态的 Pod
    filterRunning: true
    metricRelabelConfigs:
    # 过滤掉已停止的 Pod 指标
    - sourceLabels: [__name__, phase]
      action: drop
      regex: kube_pod_.*_;(Failed|Succeeded)
    
    # 针对特定工作负载类型的过滤
    - sourceLabels: [__name__, job_name, phase]
      action: drop
      regex: kube_pod_.*_;.*training.*;(Failed|Succeeded)

---
# ==========================================
# 方法 5: 使用 Kubernetes 字段选择器（在某些情况下可用）
# ==========================================
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config-with-selectors
data:
  prometheus.yml: |
    scrape_configs:
    - job_name: 'kubernetes-pods-filtered'
      kubernetes_sd_configs:
      - role: pod
        # 使用字段选择器过滤 Pod（注意：这会过滤要发现的 Pod，不是 KSM 监控的 Pod）
        selectors:
        - role: pod
          field: "status.phase!=Failed,status.phase!=Succeeded"
          # 这个配置实际上是过滤 Prometheus 要发现的 Pod
          # 对于 KSM 来说，我们通常不需要这样做
      
      relabel_configs:
      # 标准的 relabel 配置...
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true

---
# ==========================================
# 方法 6: 组合策略 - 推荐的完整解决方案
# ==========================================
apiVersion: v1
kind: ConfigMap
metadata:
  name: optimized-ksm-scrape-config
data:
  ksm-scrape.yml: |
    # 优化的 KSM 采集配置
    - job_name: 'kubernetes-pods-kube-state-metrics-optimized'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
          - kube-system
          - monitoring
      
      relabel_configs:
      # 只采集 kube-state-metrics Pod
      - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_name]
        action: keep
        regex: kube-state-metrics
      
      # 标准的地址和路径配置
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
      
      # 添加有用的标签
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)
      - action: labeldrop
        regex: (.+)_revision_hash|(.+)_template_generation
      
      metric_relabel_configs:
      # === 第一层过滤：完全丢弃已停止 Pod 的大部分指标 ===
      
      # 1. 丢弃 Failed 状态 Pod 的所有指标
      - source_labels: [__name__, phase]
        action: drop
        regex: kube_pod_.*_;Failed
      
      # 2. 丢弃 Succeeded 状态 Pod 的详细指标，但保留摘要信息
      - source_labels: [__name__, phase]
        action: drop
        regex: kube_pod_(start_time|created|status_ready|status_unschedulable|deletion_timestamp|status_reason|container_status_.*|container_resource_.*);Succeeded
      
      # === 第二层过滤：基于工作负载类型的智能过滤 ===
      
      # 3. 训练任务相关的过滤
      - source_labels: [__name__, job, phase]
        action: drop
        regex: kube_pod_.*_;.*-(training|train|inference|infer|pytorch|tensorflow|job)-.*;(Failed|Succeeded)
      
      # 4. 基于命名空间的过滤
      - source_labels: [__name__, namespace, phase]
        action: drop
        regex: kube_pod_.*_;(training|ml|ai|batch|jobs?)-.*;(Failed|Succeeded)
      
      # === 第三层过滤：保留重要指标 ===
      
      # 5. 始终保留 Job 状态指标（用于监控和告警）
      - source_labels: [__name__]
        action: keep
        regex: kube_job_(status_.*|info|spec_.*|created)
      
      # 6. 保留节点和集群级别的指标
      - source_labels: [__name__]
        action: keep
        regex: kube_(node|namespace|deployment|service|configmap|secret)_.*
      
      # 7. 有选择地保留 Pod 指标
      - source_labels: [__name__]
        action: keep
        regex: kube_pod_(info|owner|status_phase)
      
      # 8. 保留运行中 Pod 的资源指标
      - source_labels: [__name__, phase]
        action: keep
        regex: kube_pod_container_(resource_.*|status_running);Running
      
      # === 采样率控制 ===
      
      # 9. 对某些高频指标进行采样（可选）
      # 注意：Prometheus 本身不支持采样，这需要在 VictoriaMetrics 等系统中实现
      
      scrape_interval: 30s
      scrape_timeout: 10s
